<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>游戏进行中 - 德州扑克计分助手</title>
    <link rel="stylesheet" href="css/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .game-header {
            background: linear-gradient(135deg, var(--ios-green), var(--ios-teal));
            color: white;
            padding: 20px;
            margin: 16px;
            border-radius: 16px;
            text-align: center;
        }

        .game-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .game-stats {
            display: flex;
            justify-content: space-around;
            font-size: 14px;
            opacity: 0.9;
        }

        .poker-table {
            background: var(--ios-green);
            margin: 16px;
            border-radius: 20px;
            padding: 20px;
            position: relative;
            min-height: 200px;
            box-shadow: var(--shadow-medium);
        }

        .table-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            color: white;
            backdrop-filter: blur(10px);
        }

        .pot-amount {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .pot-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .blinds-info {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
        }

        .players-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px;
        }

        .player-card {
            background: var(--background-secondary);
            border-radius: 12px;
            padding: 16px;
            box-shadow: var(--shadow-light);
            border: 2px solid transparent;
            transition: all 0.2s;
            cursor: pointer;
        }

        .player-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .player-card.active {
            border-color: var(--ios-green);
            background: var(--ios-green)10;
        }

        .player-card.eliminated {
            opacity: 0.5;
            background: var(--text-quaternary)10;
        }

        .player-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .player-avatar {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-right: 8px;
        }

        .player-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            flex: 1;
        }

        .player-status {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .status-active {
            background: var(--ios-green)20;
            color: var(--ios-green);
        }

        .status-eliminated {
            background: var(--ios-red)20;
            color: var(--ios-red);
        }

        .chips-display {
            text-align: center;
        }

        .chips-amount {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .chips-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px;
        }

        .action-btn {
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-next-hand {
            background: var(--ios-blue);
            color: white;
        }

        .btn-next-hand:hover {
            background: #0056CC;
            transform: translateY(-1px);
        }

        .btn-pot-calc {
            background: var(--ios-orange);
            color: white;
        }

        .btn-pot-calc:hover {
            background: #E6850E;
            transform: translateY(-1px);
        }

        .btn-end-game {
            background: var(--ios-red);
            color: white;
            grid-column: 1 / -1;
            margin-top: 8px;
        }

        .btn-end-game:hover {
            background: #DC3545;
            transform: translateY(-1px);
        }

        .quick-actions {
            background: var(--background-secondary);
            margin: 16px;
            border-radius: 12px;
            padding: 16px;
            box-shadow: var(--shadow-light);
        }

        .quick-actions-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .quick-action-btn {
            padding: 12px 8px;
            background: var(--background-tertiary);
            border: 1px solid var(--separator);
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }

        .quick-action-btn:hover {
            background: var(--ios-blue)10;
            border-color: var(--ios-blue);
            color: var(--ios-blue);
        }

        .game-timer {
            position: absolute;
            top: 16px;
            left: 16px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--background-secondary);
            border-radius: 16px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
            box-shadow: var(--shadow-heavy);
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            text-align: center;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .modal-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .modal-btn-cancel {
            background: var(--background-tertiary);
            color: var(--text-primary);
        }

        .modal-btn-confirm {
            background: var(--ios-red);
            color: white;
        }
    </style>
</head>
<body>
    <div class="device-frame">
        <div class="device-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal" style="font-size: 12px;"></i>
                    <i class="fas fa-wifi" style="font-size: 12px;"></i>
                    <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" data-action="navigate" data-page="add-players">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">游戏进行中</div>
                <button class="nav-button" data-action="show-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 游戏头部信息 -->
                <div class="game-header fade-in" id="game-header">
                    <div class="game-title">周末扑克局</div>
                    <div class="game-stats">
                        <span>现金局</span>
                        <span>5/10</span>
                        <span id="game-duration">0小时0分钟</span>
                    </div>
                </div>

                <!-- 扑克桌 -->
                <div class="poker-table fade-in">
                    <div class="game-timer" id="game-timer">00:00:00</div>
                    <div class="blinds-info" id="blinds-info">5/10</div>
                    
                    <div class="table-center">
                        <div class="pot-amount" id="pot-amount">0</div>
                        <div class="pot-label">底池</div>
                    </div>
                </div>

                <!-- 玩家网格 -->
                <div class="players-grid" id="players-grid">
                    <!-- 动态加载玩家卡片 -->
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions fade-in">
                    <div class="quick-actions-title">
                        <i class="fas fa-bolt" style="margin-right: 8px; color: var(--ios-orange);"></i>
                        快速操作
                    </div>
                    <div class="quick-actions-grid">
                        <button class="quick-action-btn" onclick="quickChipUpdate('add', 100)">
                            +100
                        </button>
                        <button class="quick-action-btn" onclick="quickChipUpdate('subtract', 100)">
                            -100
                        </button>
                        <button class="quick-action-btn" onclick="quickChipUpdate('add', 500)">
                            +500
                        </button>
                        <button class="quick-action-btn" onclick="quickChipUpdate('subtract', 500)">
                            -500
                        </button>
                        <button class="quick-action-btn" onclick="quickChipUpdate('add', 1000)">
                            +1000
                        </button>
                        <button class="quick-action-btn" onclick="quickChipUpdate('subtract', 1000)">
                            -1000
                        </button>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-btn btn-next-hand" data-action="next-hand">
                        <i class="fas fa-forward"></i>
                        下一手牌
                    </button>
                    <button class="action-btn btn-pot-calc" data-action="show-pot-calculator">
                        <i class="fas fa-calculator"></i>
                        底池计算
                    </button>
                    <button class="action-btn btn-end-game" data-action="end-game">
                        <i class="fas fa-stop"></i>
                        结束游戏
                    </button>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <!-- 结束游戏确认模态框 -->
    <div class="modal" id="end-game-modal">
        <div class="modal-content">
            <div class="modal-title">确认结束游戏</div>
            <p style="color: var(--text-secondary); text-align: center; margin-bottom: 0;">
                游戏结束后将无法继续，确定要结束当前游戏吗？
            </p>
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-cancel" onclick="closeModal()">取消</button>
                <button class="modal-btn modal-btn-confirm" onclick="confirmEndGame()">确认结束</button>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        let selectedPlayerId = null;
        let gameStartTime = null;

        document.addEventListener('DOMContentLoaded', function() {
            initGamePlayPage();
            startGameTimer();
        });

        function initGamePlayPage() {
            const app = window.pokerApp;
            if (!app) {
                setTimeout(initGamePlayPage, 100);
                return;
            }

            const currentGame = app.loadCurrentGame();
            if (currentGame) {
                app.currentGame = currentGame;
                gameStartTime = new Date(currentGame.startTime);
                loadGameInfo(currentGame);
                loadPlayers(currentGame);
                updateGameStats();
            }
        }

        function loadGameInfo(game) {
            const header = document.getElementById('game-header');
            header.querySelector('.game-title').textContent = game.name;
            
            const stats = header.querySelector('.game-stats');
            stats.innerHTML = `
                <span>${game.type === 'cash' ? '现金局' : '锦标赛'}</span>
                <span>${game.smallBlind}/${game.bigBlind}</span>
                <span id="game-duration">0小时0分钟</span>
            `;

            document.getElementById('blinds-info').textContent = `${game.smallBlind}/${game.bigBlind}`;
        }

        function loadPlayers(game) {
            const container = document.getElementById('players-grid');
            
            container.innerHTML = game.players.map(player => `
                <div class="player-card ${player.isActive ? 'active' : 'eliminated'}" 
                     onclick="selectPlayer('${player.id}')" 
                     data-player-id="${player.id}">
                    <div class="player-header">
                        <div class="player-avatar avatar-${player.avatar || '1'}">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="player-name">${player.name}</div>
                        <div class="player-status ${player.isActive ? 'status-active' : 'status-eliminated'}">
                            ${player.isActive ? '活跃' : '淘汰'}
                        </div>
                    </div>
                    <div class="chips-display">
                        <div class="chips-amount" data-player-chips data-player-id="${player.id}">
                            ${player.currentChips.toLocaleString()}
                        </div>
                        <div class="chips-label">筹码</div>
                    </div>
                </div>
            `).join('');
        }

        function selectPlayer(playerId) {
            selectedPlayerId = playerId;
            
            // 更新选中状态
            document.querySelectorAll('.player-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            const selectedCard = document.querySelector(`[data-player-id="${playerId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }
        }

        function quickChipUpdate(type, amount) {
            if (!selectedPlayerId) {
                alert('请先选择一名玩家');
                return;
            }

            const app = window.pokerApp;
            const player = app.currentGame?.players.find(p => p.id === selectedPlayerId);
            
            if (player) {
                if (type === 'add') {
                    player.currentChips += amount;
                } else {
                    player.currentChips = Math.max(0, player.currentChips - amount);
                    if (player.currentChips === 0) {
                        player.isActive = false;
                    }
                }
                
                app.saveGames();
                app.refreshGameDisplay();
                updatePlayerCard(player);
                updateGameStats();
            }
        }

        function updatePlayerCard(player) {
            const card = document.querySelector(`[data-player-id="${player.id}"]`);
            if (card) {
                const chipsAmount = card.querySelector('.chips-amount');
                const status = card.querySelector('.player-status');
                
                chipsAmount.textContent = player.currentChips.toLocaleString();
                
                if (player.currentChips === 0 && player.isActive) {
                    player.isActive = false;
                    card.classList.remove('active');
                    card.classList.add('eliminated');
                    status.textContent = '淘汰';
                    status.className = 'player-status status-eliminated';
                }
            }
        }

        function updateGameStats() {
            const app = window.pokerApp;
            if (!app.currentGame) return;

            // 计算总底池（这里简化处理）
            const totalChips = app.currentGame.players.reduce((sum, p) => sum + p.currentChips, 0);
            const initialTotal = app.currentGame.players.reduce((sum, p) => sum + p.initialChips, 0);
            const potAmount = Math.max(0, initialTotal - totalChips);
            
            document.getElementById('pot-amount').textContent = potAmount.toLocaleString();
        }

        function startGameTimer() {
            setInterval(() => {
                if (gameStartTime) {
                    const now = new Date();
                    const duration = now - gameStartTime;
                    
                    const hours = Math.floor(duration / (1000 * 60 * 60));
                    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((duration % (1000 * 60)) / 1000);
                    
                    const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    document.getElementById('game-timer').textContent = timeString;
                    
                    const durationString = `${hours}小时${minutes}分钟`;
                    document.getElementById('game-duration').textContent = durationString;
                }
            }, 1000);
        }

        // 模态框控制
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('show');
        }

        function closeModal() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('show');
            });
        }

        function confirmEndGame() {
            closeModal();
            window.pokerApp.endGame();
        }

        // 重写结束游戏动作
        document.addEventListener('click', function(e) {
            if (e.target.matches('[data-action="end-game"]')) {
                e.preventDefault();
                showModal('end-game-modal');
            }
        });
    </script>
</body>
</html>
