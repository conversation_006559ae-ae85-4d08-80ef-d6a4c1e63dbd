<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>游戏总结 - 德州扑克计分助手</title>
    <link rel="stylesheet" href="css/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .summary-header {
            background: linear-gradient(135deg, var(--ios-green), var(--ios-teal));
            color: white;
            padding: 24px;
            margin: 16px;
            border-radius: 16px;
            text-align: center;
        }

        .summary-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .summary-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 16px;
        }

        .game-info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-top: 16px;
        }

        .info-item {
            text-align: center;
        }

        .info-value {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .info-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .winner-section {
            background: var(--background-secondary);
            margin: 16px;
            border-radius: 16px;
            padding: 20px;
            box-shadow: var(--shadow-light);
            text-align: center;
        }

        .winner-crown {
            font-size: 48px;
            color: var(--ios-yellow);
            margin-bottom: 12px;
        }

        .winner-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .winner-name {
            font-size: 24px;
            font-weight: 700;
            color: var(--ios-blue);
            margin-bottom: 8px;
        }

        .winner-chips {
            font-size: 16px;
            color: var(--text-secondary);
        }

        .rankings-section {
            margin: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            padding: 0 4px;
            display: flex;
            align-items: center;
        }

        .section-icon {
            margin-right: 8px;
            color: var(--ios-blue);
        }

        .ranking-item {
            background: var(--background-secondary);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--separator);
        }

        .ranking-position {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            margin-right: 12px;
        }

        .position-1 {
            background: var(--ios-yellow);
            color: white;
        }

        .position-2 {
            background: #C0C0C0;
            color: white;
        }

        .position-3 {
            background: #CD7F32;
            color: white;
        }

        .position-other {
            background: var(--background-tertiary);
            color: var(--text-secondary);
        }

        .player-info {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .player-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            margin-right: 12px;
        }

        .player-details {
            flex: 1;
        }

        .player-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .player-result {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .chips-result {
            text-align: right;
        }

        .final-chips {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .chips-change {
            font-size: 12px;
            font-weight: 600;
        }

        .chips-gain {
            color: var(--ios-green);
        }

        .chips-loss {
            color: var(--ios-red);
        }

        .chips-even {
            color: var(--text-secondary);
        }

        .game-stats {
            background: var(--background-secondary);
            margin: 16px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--shadow-light);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 12px;
            background: var(--background-tertiary);
            border-radius: 8px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px;
        }

        .action-btn {
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-new-game {
            background: var(--ios-blue);
            color: white;
        }

        .btn-new-game:hover {
            background: #0056CC;
            transform: translateY(-1px);
        }

        .btn-home {
            background: var(--background-secondary);
            color: var(--ios-blue);
            border: 1px solid var(--separator);
        }

        .btn-home:hover {
            background: var(--background-tertiary);
        }

        .share-section {
            background: var(--background-secondary);
            margin: 16px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--shadow-light);
            text-align: center;
        }

        .share-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .share-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
        }

        .share-btn {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .share-copy {
            background: var(--ios-blue);
        }

        .share-copy:hover {
            background: #0056CC;
            transform: scale(1.1);
        }

        .celebration {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1000;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--ios-yellow);
            animation: confetti-fall 3s linear infinite;
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="device-frame">
        <div class="device-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal" style="font-size: 12px;"></i>
                    <i class="fas fa-wifi" style="font-size: 12px;"></i>
                    <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <div></div>
                <div class="nav-title">游戏总结</div>
                <button class="nav-button" data-action="share">
                    <i class="fas fa-share"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 游戏总结头部 -->
                <div class="summary-header fade-in" id="summary-header">
                    <div class="summary-title">游戏结束</div>
                    <div class="summary-subtitle">恭喜所有参与者！</div>
                    <div class="game-info-grid">
                        <div class="info-item">
                            <div class="info-value" id="game-duration">2小时30分钟</div>
                            <div class="info-label">游戏时长</div>
                        </div>
                        <div class="info-item">
                            <div class="info-value" id="total-players">6</div>
                            <div class="info-label">参与玩家</div>
                        </div>
                        <div class="info-item">
                            <div class="info-value" id="game-type">现金局</div>
                            <div class="info-label">游戏类型</div>
                        </div>
                        <div class="info-item">
                            <div class="info-value" id="blinds-info">5/10</div>
                            <div class="info-label">盲注</div>
                        </div>
                    </div>
                </div>

                <!-- 获胜者 -->
                <div class="winner-section fade-in" id="winner-section">
                    <div class="winner-crown">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="winner-title">🎉 恭喜获胜者 🎉</div>
                    <div class="winner-name" id="winner-name">张三</div>
                    <div class="winner-chips" id="winner-chips">最终筹码: 2,500</div>
                </div>

                <!-- 排名 -->
                <div class="rankings-section">
                    <div class="section-title">
                        <i class="fas fa-trophy section-icon"></i>
                        最终排名
                    </div>
                    <div id="rankings-container">
                        <!-- 动态加载排名 -->
                    </div>
                </div>

                <!-- 游戏统计 -->
                <div class="game-stats fade-in">
                    <div class="section-title">
                        <i class="fas fa-chart-bar section-icon"></i>
                        游戏统计
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="total-hands">0</div>
                            <div class="stat-label">总手牌数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="avg-pot">0</div>
                            <div class="stat-label">平均底池</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="biggest-winner">+0</div>
                            <div class="stat-label">最大赢家</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="biggest-loser">-0</div>
                            <div class="stat-label">最大输家</div>
                        </div>
                    </div>
                </div>

                <!-- 分享 -->
                <div class="share-section fade-in">
                    <div class="share-title">分享游戏结果</div>
                    <div class="share-buttons">
                        <button class="share-btn share-copy" onclick="copyResults()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-btn btn-new-game" data-action="navigate" data-page="create-game">
                        <i class="fas fa-plus"></i>
                        新游戏
                    </button>
                    <button class="action-btn btn-home" data-action="navigate" data-page="index">
                        <i class="fas fa-home"></i>
                        返回首页
                    </button>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <!-- 庆祝动画 -->
    <div class="celebration" id="celebration"></div>

    <script src="js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initGameSummary();
            startCelebration();
        });

        function initGameSummary() {
            const app = window.pokerApp;
            if (!app) {
                setTimeout(initGameSummary, 100);
                return;
            }

            const currentGame = app.loadCurrentGame();
            if (currentGame) {
                loadGameSummary(currentGame, app);
            }
        }

        function loadGameSummary(game, app) {
            // 更新游戏信息
            document.getElementById('game-duration').textContent = 
                game.startTime && game.endTime ? app.getGameDuration(game.startTime, game.endTime) : '未知';
            document.getElementById('total-players').textContent = game.players.length;
            document.getElementById('game-type').textContent = game.type === 'cash' ? '现金局' : '锦标赛';
            document.getElementById('blinds-info').textContent = `${game.smallBlind}/${game.bigBlind}`;

            // 计算排名
            const rankings = calculateRankings(game.players);
            
            // 显示获胜者
            if (rankings.length > 0) {
                const winner = rankings[0];
                document.getElementById('winner-name').textContent = winner.name;
                document.getElementById('winner-chips').textContent = `最终筹码: ${winner.currentChips.toLocaleString()}`;
            }

            // 显示排名列表
            displayRankings(rankings);

            // 更新统计信息
            updateGameStats(game, rankings);
        }

        function calculateRankings(players) {
            // 按筹码数量排序
            return [...players].sort((a, b) => b.currentChips - a.currentChips);
        }

        function displayRankings(rankings) {
            const container = document.getElementById('rankings-container');
            
            container.innerHTML = rankings.map((player, index) => {
                const position = index + 1;
                const chipsChange = player.currentChips - player.initialChips;
                const changeClass = chipsChange > 0 ? 'chips-gain' : 
                                   chipsChange < 0 ? 'chips-loss' : 'chips-even';
                const changeText = chipsChange > 0 ? `+${chipsChange.toLocaleString()}` :
                                  chipsChange < 0 ? chipsChange.toLocaleString() : '±0';

                return `
                    <div class="ranking-item fade-in" style="animation-delay: ${index * 0.1}s;">
                        <div class="ranking-position ${getPositionClass(position)}">
                            ${position <= 3 ? getPositionIcon(position) : position}
                        </div>
                        <div class="player-info">
                            <div class="player-avatar avatar-${player.avatar || '1'}">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="player-details">
                                <div class="player-name">${player.name}</div>
                                <div class="player-result">
                                    ${player.isActive ? '完成游戏' : '已淘汰'}
                                </div>
                            </div>
                        </div>
                        <div class="chips-result">
                            <div class="final-chips">${player.currentChips.toLocaleString()}</div>
                            <div class="chips-change ${changeClass}">${changeText}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getPositionClass(position) {
            if (position === 1) return 'position-1';
            if (position === 2) return 'position-2';
            if (position === 3) return 'position-3';
            return 'position-other';
        }

        function getPositionIcon(position) {
            const icons = ['', '🥇', '🥈', '🥉'];
            return icons[position] || position;
        }

        function updateGameStats(game, rankings) {
            // 简化的统计计算
            const totalHands = Math.floor(Math.random() * 50) + 20; // 模拟数据
            const avgPot = Math.floor(Math.random() * 500) + 100;
            
            const biggestWinner = rankings[0];
            const biggestLoser = rankings[rankings.length - 1];
            
            const biggestWin = biggestWinner.currentChips - biggestWinner.initialChips;
            const biggestLoss = biggestLoser.currentChips - biggestLoser.initialChips;

            document.getElementById('total-hands').textContent = totalHands;
            document.getElementById('avg-pot').textContent = avgPot.toLocaleString();
            document.getElementById('biggest-winner').textContent = `+${biggestWin.toLocaleString()}`;
            document.getElementById('biggest-loser').textContent = biggestLoss.toLocaleString();
        }

        function startCelebration() {
            const celebration = document.getElementById('celebration');
            const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
            
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.className = 'confetti';
                    confetti.style.left = Math.random() * 100 + '%';
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.animationDelay = Math.random() * 3 + 's';
                    confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                    
                    celebration.appendChild(confetti);
                    
                    setTimeout(() => {
                        confetti.remove();
                    }, 5000);
                }, i * 100);
            }
        }

        function copyResults() {
            const app = window.pokerApp;
            const currentGame = app.loadCurrentGame();
            
            if (!currentGame) return;
            
            const rankings = calculateRankings(currentGame.players);
            const duration = currentGame.startTime && currentGame.endTime ? 
                app.getGameDuration(currentGame.startTime, currentGame.endTime) : '未知';
            
            let resultText = `🎮 德州扑克游戏结果 🎮\n\n`;
            resultText += `游戏名称: ${currentGame.name}\n`;
            resultText += `游戏类型: ${currentGame.type === 'cash' ? '现金局' : '锦标赛'}\n`;
            resultText += `盲注: ${currentGame.smallBlind}/${currentGame.bigBlind}\n`;
            resultText += `游戏时长: ${duration}\n\n`;
            resultText += `🏆 最终排名:\n`;
            
            rankings.forEach((player, index) => {
                const position = index + 1;
                const chipsChange = player.currentChips - player.initialChips;
                const changeText = chipsChange > 0 ? `(+${chipsChange.toLocaleString()})` :
                                  chipsChange < 0 ? `(${chipsChange.toLocaleString()})` : '(±0)';
                
                resultText += `${position}. ${player.name}: ${player.currentChips.toLocaleString()} ${changeText}\n`;
            });
            
            resultText += `\n🎯 使用德州扑克计分助手记录`;
            
            // 复制到剪贴板
            navigator.clipboard.writeText(resultText).then(() => {
                alert('游戏结果已复制到剪贴板！');
            }).catch(() => {
                alert('复制失败，请手动复制结果');
            });
        }
    </script>
</body>
</html>
