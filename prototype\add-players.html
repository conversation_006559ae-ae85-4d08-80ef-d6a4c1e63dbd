<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>添加玩家 - 德州扑克计分助手</title>
    <link rel="stylesheet" href="css/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .game-info-card {
            background: linear-gradient(135deg, var(--ios-blue), var(--ios-purple));
            color: white;
            margin: 16px;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
        }

        .game-name {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .game-details {
            font-size: 14px;
            opacity: 0.9;
            display: flex;
            justify-content: space-around;
            margin-top: 12px;
        }

        .add-player-section {
            background: var(--background-secondary);
            margin: 16px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--shadow-light);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .section-icon {
            margin-right: 8px;
            color: var(--ios-blue);
        }

        .player-input-group {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-bottom: 16px;
        }

        .avatar-selector {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: var(--ios-blue)20;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid var(--ios-blue)30;
        }

        .avatar-selector:hover {
            background: var(--ios-blue)30;
            transform: scale(1.05);
        }

        .avatar-selector.selected {
            background: var(--ios-blue);
            color: white;
        }

        .player-name-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid var(--separator);
            border-radius: 10px;
            font-size: 16px;
            background: var(--background-tertiary);
        }

        .add-player-btn {
            padding: 12px 20px;
            background: var(--ios-green);
            color: white;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .add-player-btn:hover {
            background: #28A745;
            transform: translateY(-1px);
        }

        .add-player-btn:disabled {
            background: var(--text-quaternary);
            cursor: not-allowed;
            transform: none;
        }

        .players-list {
            margin: 16px;
        }

        .players-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 0 4px;
        }

        .players-count {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .min-players-note {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .player-item {
            background: var(--background-secondary);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--separator);
            transition: all 0.2s;
        }

        .player-item:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }

        .player-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .player-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: var(--ios-blue);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
        }

        .player-details {
            flex: 1;
        }

        .player-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .player-chips {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .seat-number {
            background: var(--background-tertiary);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 12px;
        }

        .remove-btn {
            background: var(--ios-red);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .remove-btn:hover {
            background: #DC3545;
        }

        .start-game-section {
            padding: 16px;
            background: var(--background-secondary);
            border-top: 0.5px solid var(--separator);
            position: sticky;
            bottom: var(--home-indicator-height);
        }

        .start-game-btn {
            width: 100%;
            padding: 16px;
            background: var(--ios-green);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: var(--shadow-medium);
        }

        .start-game-btn:hover {
            background: #28A745;
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
        }

        .start-game-btn:disabled {
            background: var(--text-quaternary);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .empty-players {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .avatar-options {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            margin: 12px 0;
        }

        .avatar-option {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }

        .avatar-option.selected {
            border-color: var(--ios-blue);
            transform: scale(1.1);
        }

        .avatar-1 { background: var(--ios-blue); color: white; }
        .avatar-2 { background: var(--ios-green); color: white; }
        .avatar-3 { background: var(--ios-red); color: white; }
        .avatar-4 { background: var(--ios-orange); color: white; }
        .avatar-5 { background: var(--ios-purple); color: white; }
        .avatar-6 { background: var(--ios-pink); color: white; }
    </style>
</head>
<body>
    <div class="device-frame">
        <div class="device-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal" style="font-size: 12px;"></i>
                    <i class="fas fa-wifi" style="font-size: 12px;"></i>
                    <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" data-action="navigate" data-page="create-game">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">添加玩家</div>
                <div></div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 游戏信息 -->
                <div class="game-info-card fade-in" id="game-info">
                    <div class="game-name">周末扑克局</div>
                    <div class="game-details">
                        <span>现金局</span>
                        <span>5/10</span>
                        <span>1000筹码</span>
                    </div>
                </div>

                <!-- 添加玩家 -->
                <div class="add-player-section fade-in">
                    <div class="section-title">
                        <i class="fas fa-user-plus section-icon"></i>
                        添加新玩家
                    </div>
                    
                    <form id="add-player-form" data-action="add-player">
                        <div class="avatar-options">
                            <div class="avatar-option avatar-1 selected" data-avatar="1">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="avatar-option avatar-2" data-avatar="2">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="avatar-option avatar-3" data-avatar="3">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="avatar-option avatar-4" data-avatar="4">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="avatar-option avatar-5" data-avatar="5">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="avatar-option avatar-6" data-avatar="6">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        
                        <div class="player-input-group">
                            <input type="text" name="playerName" class="player-name-input" placeholder="输入玩家姓名" required maxlength="10">
                            <input type="hidden" name="avatar" value="1">
                            <button type="submit" class="add-player-btn">
                                <i class="fas fa-plus"></i> 添加
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 玩家列表 -->
                <div class="players-list">
                    <div class="players-header">
                        <div class="players-count">玩家列表 (<span id="player-count">0</span>/10)</div>
                        <div class="min-players-note">至少需要2名玩家</div>
                    </div>
                    
                    <div id="players-container">
                        <!-- 动态加载玩家列表 -->
                    </div>
                </div>
            </div>

            <!-- 开始游戏按钮 -->
            <div class="start-game-section">
                <button class="start-game-btn" id="start-game-btn" data-action="start-game" disabled>
                    <i class="fas fa-play"></i> 开始游戏
                </button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initAddPlayersPage();
        });

        function initAddPlayersPage() {
            // 加载当前游戏信息
            loadGameInfo();
            
            // 初始化头像选择
            initAvatarSelection();
            
            // 初始化表单
            initPlayerForm();
            
            // 更新玩家列表
            updatePlayersList();
        }

        function loadGameInfo() {
            const app = window.pokerApp;
            if (!app) {
                setTimeout(loadGameInfo, 100);
                return;
            }

            const currentGame = app.loadCurrentGame();
            if (currentGame) {
                const gameInfoCard = document.getElementById('game-info');
                gameInfoCard.querySelector('.game-name').textContent = currentGame.name;
                
                const details = gameInfoCard.querySelector('.game-details');
                details.innerHTML = `
                    <span>${currentGame.type === 'cash' ? '现金局' : '锦标赛'}</span>
                    <span>${currentGame.smallBlind}/${currentGame.bigBlind}</span>
                    <span>${currentGame.initialChips.toLocaleString()}筹码</span>
                `;
            }
        }

        function initAvatarSelection() {
            const avatarOptions = document.querySelectorAll('.avatar-option');
            const avatarInput = document.querySelector('input[name="avatar"]');
            
            avatarOptions.forEach(option => {
                option.addEventListener('click', function() {
                    avatarOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    avatarInput.value = this.dataset.avatar;
                });
            });
        }

        function initPlayerForm() {
            const form = document.getElementById('add-player-form');
            const nameInput = form.querySelector('input[name="playerName"]');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                
                if (validatePlayerData(data)) {
                    addPlayer(data);
                    form.reset();
                    // 重置头像选择
                    document.querySelectorAll('.avatar-option').forEach(opt => opt.classList.remove('selected'));
                    document.querySelector('.avatar-option[data-avatar="1"]').classList.add('selected');
                    document.querySelector('input[name="avatar"]').value = '1';
                }
            });
            
            // 实时验证
            nameInput.addEventListener('input', function() {
                const submitBtn = form.querySelector('.add-player-btn');
                submitBtn.disabled = !this.value.trim();
            });
        }

        function validatePlayerData(data) {
            const app = window.pokerApp;
            const currentGame = app.loadCurrentGame();
            
            if (!data.playerName.trim()) {
                alert('请输入玩家姓名');
                return false;
            }
            
            if (currentGame && currentGame.players.length >= 10) {
                alert('最多只能添加10名玩家');
                return false;
            }
            
            // 检查重名
            if (currentGame && currentGame.players.some(p => p.name === data.playerName.trim())) {
                alert('玩家姓名已存在');
                return false;
            }
            
            return true;
        }

        function addPlayer(data) {
            const app = window.pokerApp;
            app.addPlayerToGame(data);
            updatePlayersList();
        }

        function updatePlayersList() {
            const app = window.pokerApp;
            const currentGame = app.loadCurrentGame();
            const container = document.getElementById('players-container');
            const countElement = document.getElementById('player-count');
            const startBtn = document.getElementById('start-game-btn');
            
            if (!currentGame || currentGame.players.length === 0) {
                container.innerHTML = `
                    <div class="empty-players">
                        <div class="empty-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">还没有玩家</div>
                        <div style="font-size: 14px;">请添加至少2名玩家开始游戏</div>
                    </div>
                `;
                countElement.textContent = '0';
                startBtn.disabled = true;
                return;
            }
            
            container.innerHTML = currentGame.players.map((player, index) => `
                <div class="player-item fade-in">
                    <div class="player-info">
                        <div class="player-avatar avatar-${player.avatar || '1'}">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="player-details">
                            <div class="player-name">${player.name}</div>
                            <div class="player-chips">初始筹码: ${player.initialChips.toLocaleString()}</div>
                        </div>
                    </div>
                    <div class="seat-number">座位 ${index + 1}</div>
                    <button class="remove-btn" onclick="removePlayer('${player.id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
            
            countElement.textContent = currentGame.players.length;
            startBtn.disabled = currentGame.players.length < 2;
        }

        function removePlayer(playerId) {
            if (confirm('确定要移除这名玩家吗？')) {
                const app = window.pokerApp;
                app.removePlayer(playerId);
                updatePlayersList();
            }
        }
    </script>
</body>
</html>
