// 德州扑克计分App - 主要JavaScript逻辑

class PokerScoreApp {
    constructor() {
        this.currentGame = null;
        this.games = this.loadGames();
        this.players = this.loadPlayers();
        this.init();
    }

    init() {
        this.updateStatusBar();
        this.bindEvents();
        
        // 每分钟更新一次时间
        setInterval(() => this.updateStatusBar(), 60000);
    }

    // 更新状态栏时间
    updateStatusBar() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        });
        
        const timeElement = document.querySelector('.status-time');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // 绑定事件
    bindEvents() {
        // 导航按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action]')) {
                const action = e.target.getAttribute('data-action');
                this.handleAction(action, e.target);
            }
        });

        // 表单提交事件
        document.addEventListener('submit', (e) => {
            e.preventDefault();
            const form = e.target;
            const action = form.getAttribute('data-action');
            if (action) {
                this.handleFormSubmit(action, form);
            }
        });
    }

    // 处理动作
    handleAction(action, element) {
        switch (action) {
            case 'navigate':
                const page = element.getAttribute('data-page');
                this.navigateTo(page);
                break;
            case 'back':
                this.goBack();
                break;
            case 'create-game':
                this.showCreateGameForm();
                break;
            case 'start-game':
                this.startGame();
                break;
            case 'add-player':
                this.addPlayer();
                break;
            case 'remove-player':
                const playerId = element.getAttribute('data-player-id');
                this.removePlayer(playerId);
                break;
            case 'update-chips':
                this.showChipUpdateModal(element);
                break;
            case 'end-game':
                this.endGame();
                break;
            case 'view-game':
                const gameId = element.getAttribute('data-game-id');
                this.viewGame(gameId);
                break;
            case 'next-hand':
                this.nextHand();
                break;
            case 'show-pot-calculator':
                this.showPotCalculator();
                break;
        }
    }

    // 处理表单提交
    handleFormSubmit(action, form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        switch (action) {
            case 'create-game':
                this.createGame(data);
                break;
            case 'add-player':
                this.addPlayerToGame(data);
                break;
            case 'update-chips':
                this.updatePlayerChips(data);
                break;
        }
    }

    // 页面导航
    navigateTo(page) {
        window.location.href = `${page}.html`;
    }

    goBack() {
        window.history.back();
    }

    // 游戏管理
    createGame(data) {
        const game = {
            id: this.generateId(),
            name: data.gameName,
            type: data.gameType,
            smallBlind: parseInt(data.smallBlind),
            bigBlind: parseInt(data.bigBlind),
            initialChips: parseInt(data.initialChips),
            players: [],
            status: 'created',
            createTime: new Date().toISOString(),
            startTime: null,
            endTime: null
        };

        this.games.push(game);
        this.saveGames();
        this.currentGame = game;
        
        this.navigateTo('add-players');
    }

    startGame() {
        if (this.currentGame && this.currentGame.players.length >= 2) {
            this.currentGame.status = 'playing';
            this.currentGame.startTime = new Date().toISOString();
            this.saveGames();
            this.navigateTo('game-play');
        } else {
            this.showAlert('至少需要2名玩家才能开始游戏');
        }
    }

    endGame() {
        if (this.currentGame) {
            this.currentGame.status = 'finished';
            this.currentGame.endTime = new Date().toISOString();
            this.saveGames();
            this.navigateTo('game-summary');
        }
    }

    // 玩家管理
    addPlayerToGame(data) {
        if (!this.currentGame) return;

        const player = {
            id: this.generateId(),
            name: data.playerName,
            avatar: data.avatar || 'default',
            currentChips: this.currentGame.initialChips,
            initialChips: this.currentGame.initialChips,
            isActive: true,
            seatNumber: this.currentGame.players.length + 1
        };

        this.currentGame.players.push(player);
        this.saveGames();
        this.refreshPlayerList();
    }

    removePlayer(playerId) {
        if (!this.currentGame) return;

        this.currentGame.players = this.currentGame.players.filter(p => p.id !== playerId);
        this.saveGames();
        this.refreshPlayerList();
    }

    updatePlayerChips(data) {
        if (!this.currentGame) return;

        const player = this.currentGame.players.find(p => p.id === data.playerId);
        if (player) {
            const amount = parseInt(data.amount);
            const type = data.type; // 'add' or 'subtract'
            
            if (type === 'add') {
                player.currentChips += amount;
            } else {
                player.currentChips = Math.max(0, player.currentChips - amount);
            }
            
            this.saveGames();
            this.refreshGameDisplay();
        }
    }

    // 界面更新
    refreshPlayerList() {
        const container = document.querySelector('.players-list');
        if (!container || !this.currentGame) return;

        container.innerHTML = this.currentGame.players.map(player => `
            <div class="list-item">
                <div class="flex align-center">
                    <div class="player-avatar">
                        <i class="fas fa-user-circle" style="font-size: 24px; color: var(--ios-blue);"></i>
                    </div>
                    <div class="ml-2">
                        <div class="player-name">${player.name}</div>
                        <div class="player-chips">筹码: ${player.currentChips.toLocaleString()}</div>
                    </div>
                </div>
                <button class="btn-destructive" data-action="remove-player" data-player-id="${player.id}">
                    移除
                </button>
            </div>
        `).join('');
    }

    refreshGameDisplay() {
        // 更新游戏界面显示
        const elements = document.querySelectorAll('[data-player-chips]');
        elements.forEach(el => {
            const playerId = el.getAttribute('data-player-id');
            const player = this.currentGame?.players.find(p => p.id === playerId);
            if (player) {
                el.textContent = player.currentChips.toLocaleString();
            }
        });
    }

    // 数据持久化
    saveGames() {
        localStorage.setItem('poker_games', JSON.stringify(this.games));
        if (this.currentGame) {
            localStorage.setItem('current_game', JSON.stringify(this.currentGame));
        }
    }

    loadGames() {
        const saved = localStorage.getItem('poker_games');
        return saved ? JSON.parse(saved) : [];
    }

    loadPlayers() {
        const saved = localStorage.getItem('poker_players');
        return saved ? JSON.parse(saved) : [];
    }

    loadCurrentGame() {
        const saved = localStorage.getItem('current_game');
        return saved ? JSON.parse(saved) : null;
    }

    // 工具方法
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    showAlert(message) {
        alert(message);
    }

    showChipUpdateModal(element) {
        const playerId = element.getAttribute('data-player-id');
        const player = this.currentGame?.players.find(p => p.id === playerId);
        
        if (player) {
            const amount = prompt(`更新 ${player.name} 的筹码\n当前筹码: ${player.currentChips}\n请输入变动金额 (正数增加，负数减少):`);
            
            if (amount !== null && !isNaN(amount)) {
                const changeAmount = parseInt(amount);
                player.currentChips = Math.max(0, player.currentChips + changeAmount);
                this.saveGames();
                this.refreshGameDisplay();
            }
        }
    }

    // 格式化时间
    formatTime(isoString) {
        const date = new Date(isoString);
        return date.toLocaleString('zh-CN');
    }

    // 计算游戏时长
    getGameDuration(startTime, endTime = null) {
        const start = new Date(startTime);
        const end = endTime ? new Date(endTime) : new Date();
        const duration = end - start;

        const hours = Math.floor(duration / (1000 * 60 * 60));
        const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));

        return `${hours}小时${minutes}分钟`;
    }

    // 下一手牌
    nextHand() {
        if (!this.currentGame) return;

        // 重置所有玩家状态
        this.currentGame.players.forEach(player => {
            if (player.isActive && player.currentChips > 0) {
                // 玩家仍然活跃
            } else if (player.currentChips <= 0) {
                player.isActive = false;
            }
        });

        this.saveGames();
        this.refreshGameDisplay();
    }

    // 显示底池计算器
    showPotCalculator() {
        // 简单的底池计算
        if (!this.currentGame) return;

        const activePlayers = this.currentGame.players.filter(p => p.isActive);
        const totalPot = activePlayers.reduce((sum, player) => {
            // 这里可以添加更复杂的底池计算逻辑
            return sum;
        }, 0);

        alert(`当前底池: ${totalPot.toLocaleString()}`);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.pokerApp = new PokerScoreApp();
});

// 页面特定初始化
function initPage(pageName) {
    const app = window.pokerApp;
    if (!app) return;

    switch (pageName) {
        case 'home':
            initHomePage(app);
            break;
        case 'add-players':
            initAddPlayersPage(app);
            break;
        case 'game-play':
            initGamePlayPage(app);
            break;
        case 'history':
            initHistoryPage(app);
            break;
    }
}

function initHomePage(app) {
    // 显示最近的游戏
    const recentGames = app.games.slice(-3).reverse();
    const container = document.querySelector('.recent-games');
    
    if (container && recentGames.length > 0) {
        container.innerHTML = recentGames.map(game => `
            <div class="card" data-action="view-game" data-game-id="${game.id}">
                <h3>${game.name}</h3>
                <p>类型: ${game.type === 'cash' ? '现金局' : '锦标赛'}</p>
                <p>状态: ${getGameStatusText(game.status)}</p>
                <p>创建时间: ${app.formatTime(game.createTime)}</p>
            </div>
        `).join('');
    }
}

function initAddPlayersPage(app) {
    app.currentGame = app.loadCurrentGame();
    if (app.currentGame) {
        app.refreshPlayerList();
    }
}

function initGamePlayPage(app) {
    app.currentGame = app.loadCurrentGame();
    if (app.currentGame) {
        app.refreshGameDisplay();
    }
}

function initHistoryPage(app) {
    const container = document.querySelector('.games-history');
    if (container) {
        container.innerHTML = app.games.map(game => `
            <div class="list-item" data-action="view-game" data-game-id="${game.id}">
                <div>
                    <div class="game-name">${game.name}</div>
                    <div class="game-info">${getGameStatusText(game.status)} • ${app.formatTime(game.createTime)}</div>
                </div>
                <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
            </div>
        `).join('');
    }
}

function getGameStatusText(status) {
    const statusMap = {
        'created': '已创建',
        'playing': '进行中',
        'finished': '已结束'
    };
    return statusMap[status] || status;
}
