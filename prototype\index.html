<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>德州扑克计分助手</title>
    <link rel="stylesheet" href="css/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, var(--ios-blue), var(--ios-purple));
            color: white;
            padding: 40px 20px;
            text-align: center;
            margin: 16px;
            border-radius: 16px;
        }

        .hero-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .hero-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 24px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px;
        }

        .action-card {
            background: var(--background-secondary);
            border-radius: 16px;
            padding: 24px 16px;
            text-align: center;
            box-shadow: var(--shadow-light);
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid var(--separator);
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 12px;
            color: var(--ios-blue);
        }

        .action-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .action-subtitle {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .section-header {
            padding: 16px 16px 8px;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .recent-games {
            margin: 0 16px 16px;
        }

        .game-card {
            background: var(--background-secondary);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--shadow-light);
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid var(--separator);
        }

        .game-card:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }

        .game-name {
            font-size: 17px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .game-type {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .game-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
        }

        .status-playing {
            background: var(--ios-green)20;
            color: var(--ios-green);
        }

        .status-finished {
            background: var(--text-quaternary)20;
            color: var(--text-secondary);
        }

        .game-time {
            font-size: 13px;
            color: var(--text-tertiary);
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-subtitle {
            font-size: 14px;
            line-height: 1.4;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin: 16px;
        }

        .stat-card {
            background: var(--background-secondary);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--separator);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--ios-blue);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="device-frame">
        <div class="device-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal" style="font-size: 12px;"></i>
                    <i class="fas fa-wifi" style="font-size: 12px;"></i>
                    <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <div></div>
                <div class="nav-title">德州扑克计分</div>
                <button class="nav-button" data-action="navigate" data-page="settings">
                    <i class="fas fa-cog"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 英雄区域 -->
                <div class="hero-section fade-in">
                    <div class="hero-title">欢迎使用</div>
                    <div class="hero-subtitle">专业的德州扑克计分工具</div>
                    <button class="btn-primary" data-action="navigate" data-page="create-game" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3);">
                        <i class="fas fa-plus"></i> 开始新游戏
                    </button>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <div class="action-card" data-action="navigate" data-page="create-game">
                        <div class="action-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="action-title">新建游戏</div>
                        <div class="action-subtitle">创建新的扑克游戏</div>
                    </div>
                    
                    <div class="action-card" data-action="navigate" data-page="history">
                        <div class="action-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <div class="action-title">历史记录</div>
                        <div class="action-subtitle">查看过往游戏</div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-games">0</div>
                        <div class="stat-label">总游戏数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="active-games">0</div>
                        <div class="stat-label">进行中</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="finished-games">0</div>
                        <div class="stat-label">已完成</div>
                    </div>
                </div>

                <!-- 最近游戏 -->
                <div class="section-header">最近游戏</div>
                <div class="recent-games" id="recent-games-container">
                    <!-- 动态加载最近游戏 -->
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化首页
            initHomePage();
        });

        function initHomePage() {
            const app = window.pokerApp;
            if (!app) {
                setTimeout(initHomePage, 100);
                return;
            }

            // 更新统计信息
            updateStats(app);
            
            // 显示最近游戏
            displayRecentGames(app);
        }

        function updateStats(app) {
            const totalGames = app.games.length;
            const activeGames = app.games.filter(g => g.status === 'playing').length;
            const finishedGames = app.games.filter(g => g.status === 'finished').length;

            document.getElementById('total-games').textContent = totalGames;
            document.getElementById('active-games').textContent = activeGames;
            document.getElementById('finished-games').textContent = finishedGames;
        }

        function displayRecentGames(app) {
            const container = document.getElementById('recent-games-container');
            const recentGames = app.games.slice(-3).reverse();

            if (recentGames.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-cards-blank"></i>
                        </div>
                        <div class="empty-title">还没有游戏记录</div>
                        <div class="empty-subtitle">点击上方按钮开始您的第一场游戏</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = recentGames.map(game => `
                <div class="game-card" data-action="view-game" data-game-id="${game.id}">
                    <div class="game-name">${game.name}</div>
                    <div class="game-info">
                        <span class="game-type">${game.type === 'cash' ? '现金局' : '锦标赛'}</span>
                        <span class="game-status ${game.status === 'playing' ? 'status-playing' : 'status-finished'}">
                            ${getGameStatusText(game.status)}
                        </span>
                    </div>
                    <div class="game-time">${app.formatTime(game.createTime)}</div>
                </div>
            `).join('');
        }

        function getGameStatusText(status) {
            const statusMap = {
                'created': '已创建',
                'playing': '进行中',
                'finished': '已结束'
            };
            return statusMap[status] || status;
        }
    </script>
</body>
</html>
