<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>德州扑克计分助手</title>
    <link rel="stylesheet" href="css/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 主内容滚动优化 */
        .main-content {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        /* 英雄区域优化 */
        .hero-section {
            background: linear-gradient(135deg, var(--ios-blue), var(--ios-purple));
            color: white;
            padding: 32px 20px;
            text-align: center;
            margin: 16px;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=400&h=300&fit=crop&crop=center') center/cover;
            opacity: 0.1;
            z-index: 0;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 24px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .hero-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
        }

        .hero-stat {
            text-align: center;
        }

        .hero-stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .hero-stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 快速操作区域优化 */
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px;
        }

        .action-card {
            background: var(--background-secondary);
            border-radius: 16px;
            padding: 20px 16px;
            text-align: center;
            box-shadow: var(--shadow-light);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--separator);
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .action-card:hover::before {
            left: 100%;
        }

        .action-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: var(--shadow-heavy);
        }

        .action-card:active {
            transform: translateY(-2px) scale(0.98);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 12px;
            color: var(--ios-blue);
            transition: all 0.3s;
        }

        .action-card:hover .action-icon {
            transform: scale(1.1);
            color: var(--ios-purple);
        }

        .action-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .action-subtitle {
            font-size: 13px;
            color: var(--text-secondary);
        }

        /* 区域标题优化 */
        .section-header {
            padding: 20px 16px 12px;
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-icon {
            margin-right: 8px;
            color: var(--ios-blue);
        }

        .section-action {
            font-size: 14px;
            color: var(--ios-blue);
            cursor: pointer;
            font-weight: 500;
        }

        /* 最近游戏区域优化 */
        .recent-games {
            margin: 0 16px 20px;
        }

        .games-scroll-container {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding: 4px 0 12px;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .games-scroll-container::-webkit-scrollbar {
            display: none;
        }

        .game-card {
            background: var(--background-secondary);
            border-radius: 16px;
            padding: 16px;
            min-width: 280px;
            box-shadow: var(--shadow-light);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--separator);
            scroll-snap-align: start;
            position: relative;
            overflow: hidden;
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--ios-blue), var(--ios-purple));
            transform: scaleX(0);
            transition: transform 0.3s;
        }

        .game-card:hover::before {
            transform: scaleX(1);
        }

        .game-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-heavy);
        }

        .game-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .game-name {
            font-size: 17px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .game-type {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .game-status {
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-playing {
            background: var(--ios-green);
            color: white;
            animation: pulse 2s infinite;
        }

        .status-finished {
            background: var(--text-quaternary);
            color: white;
        }

        .status-created {
            background: var(--ios-blue);
            color: white;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .game-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }

        .game-detail {
            text-align: center;
            padding: 8px;
            background: var(--background-tertiary);
            border-radius: 8px;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .detail-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .game-time {
            font-size: 12px;
            color: var(--text-tertiary);
            text-align: center;
            padding: 8px;
            background: var(--background-tertiary);
            border-radius: 8px;
        }

        .game-players {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
        }

        .players-avatars {
            display: flex;
            gap: -4px;
        }

        .mini-avatar {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            border: 2px solid var(--background-secondary);
            margin-left: -4px;
        }

        .mini-avatar:first-child {
            margin-left: 0;
        }

        .players-count {
            font-size: 12px;
            color: var(--text-secondary);
            margin-left: 8px;
        }

        /* 空状态优化 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
            background: var(--background-secondary);
            border-radius: 16px;
            margin: 16px;
            border: 2px dashed var(--separator);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.3;
            color: var(--ios-blue);
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .empty-subtitle {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 24px;
        }

        .empty-action {
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .empty-action:hover {
            background: #0056CC;
            transform: translateY(-2px);
        }

        /* 统计卡片优化 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin: 16px;
        }

        .stat-card {
            background: var(--background-secondary);
            border-radius: 16px;
            padding: 20px 16px;
            text-align: center;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--separator);
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--ios-blue);
            transform: scaleX(0);
            transition: transform 0.3s;
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            color: var(--ios-blue);
            margin-bottom: 4px;
            transition: all 0.3s;
        }

        .stat-card:hover .stat-number {
            transform: scale(1.1);
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 继续游戏按钮 */
        .continue-game-section {
            margin: 16px;
            background: linear-gradient(135deg, var(--ios-green), var(--ios-teal));
            border-radius: 16px;
            padding: 20px;
            color: white;
            display: none;
        }

        .continue-game-section.show {
            display: block;
        }

        .continue-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .continue-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 16px;
        }

        .continue-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 12px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
        }

        .continue-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        /* 下拉刷新指示器 */
        .pull-refresh {
            position: absolute;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--ios-blue);
            font-size: 20px;
            transition: all 0.3s;
            opacity: 0;
        }

        .pull-refresh.show {
            opacity: 1;
            top: 20px;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--separator);
            border-radius: 50%;
            border-top-color: var(--ios-blue);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 触觉反馈模拟 */
        .haptic-feedback {
            animation: haptic 0.1s ease-in-out;
        }

        @keyframes haptic {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(0.95); }
        }
    </style>
</head>
<body>
    <div class="device-frame">
        <div class="device-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal" style="font-size: 12px;"></i>
                    <i class="fas fa-wifi" style="font-size: 12px;"></i>
                    <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <div></div>
                <div class="nav-title">德州扑克计分</div>
                <button class="nav-button" data-action="navigate" data-page="settings">
                    <i class="fas fa-cog"></i>
                </button>
            </div>

            <!-- 下拉刷新指示器 -->
            <div class="pull-refresh" id="pull-refresh">
                <i class="fas fa-arrow-down"></i>
            </div>

            <!-- 主内容 -->
            <div class="main-content" id="main-content">
                <!-- 英雄区域 -->
                <div class="hero-section fade-in">
                    <div class="hero-content">
                        <div class="hero-title">欢迎使用</div>
                        <div class="hero-subtitle">专业的德州扑克计分工具</div>
                        <button class="btn-primary" data-action="navigate" data-page="create-game" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3);">
                            <i class="fas fa-plus"></i> 开始新游戏
                        </button>
                        <div class="hero-stats">
                            <div class="hero-stat">
                                <div class="hero-stat-number" id="hero-total-games">0</div>
                                <div class="hero-stat-label">总游戏</div>
                            </div>
                            <div class="hero-stat">
                                <div class="hero-stat-number" id="hero-total-time">0h</div>
                                <div class="hero-stat-label">总时长</div>
                            </div>
                            <div class="hero-stat">
                                <div class="hero-stat-number" id="hero-win-rate">0%</div>
                                <div class="hero-stat-label">胜率</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 继续游戏 -->
                <div class="continue-game-section" id="continue-game-section">
                    <div class="continue-title">
                        <i class="fas fa-play-circle" style="margin-right: 8px;"></i>
                        继续游戏
                    </div>
                    <div class="continue-subtitle" id="continue-game-name">周末扑克局</div>
                    <button class="continue-btn" id="continue-game-btn" data-action="continue-game">
                        <i class="fas fa-play"></i> 继续游戏
                    </button>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <div class="action-card" data-action="navigate" data-page="create-game">
                        <div class="action-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="action-title">新建游戏</div>
                        <div class="action-subtitle">创建新的扑克游戏</div>
                    </div>

                    <div class="action-card" data-action="navigate" data-page="history">
                        <div class="action-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <div class="action-title">历史记录</div>
                        <div class="action-subtitle">查看过往游戏</div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="stats-grid">
                    <div class="stat-card" data-action="show-stats" data-type="total">
                        <div class="stat-number" id="total-games">0</div>
                        <div class="stat-label">总游戏数</div>
                    </div>
                    <div class="stat-card" data-action="show-stats" data-type="active">
                        <div class="stat-number" id="active-games">0</div>
                        <div class="stat-label">进行中</div>
                    </div>
                    <div class="stat-card" data-action="show-stats" data-type="finished">
                        <div class="stat-number" id="finished-games">0</div>
                        <div class="stat-label">已完成</div>
                    </div>
                </div>

                <!-- 最近游戏 -->
                <div class="section-header">
                    <div>
                        <i class="fas fa-clock section-icon"></i>
                        最近游戏
                    </div>
                    <div class="section-action" data-action="navigate" data-page="history">
                        查看全部
                    </div>
                </div>
                <div class="recent-games" id="recent-games-container">
                    <!-- 动态加载最近游戏 -->
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        let pullRefreshActive = false;
        let startY = 0;
        let currentY = 0;
        let refreshThreshold = 80;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化首页
            initHomePage();

            // 初始化下拉刷新
            initPullRefresh();

            // 初始化触觉反馈
            initHapticFeedback();

            // 定时更新数据
            setInterval(updateLiveData, 30000); // 每30秒更新一次
        });

        function initHomePage() {
            const app = window.pokerApp;
            if (!app) {
                setTimeout(initHomePage, 100);
                return;
            }

            // 更新所有数据
            updateStats(app);
            updateHeroStats(app);
            displayRecentGames(app);
            checkContinueGame(app);

            // 添加动画延迟
            addStaggeredAnimation();
        }

        function updateStats(app) {
            const totalGames = app.games.length;
            const activeGames = app.games.filter(g => g.status === 'playing').length;
            const finishedGames = app.games.filter(g => g.status === 'finished').length;

            // 数字动画效果
            animateNumber('total-games', totalGames);
            animateNumber('active-games', activeGames);
            animateNumber('finished-games', finishedGames);
        }

        function updateHeroStats(app) {
            const totalGames = app.games.length;

            // 计算总游戏时长
            const totalDuration = app.games.reduce((total, game) => {
                if (game.startTime) {
                    const start = new Date(game.startTime);
                    const end = game.endTime ? new Date(game.endTime) : new Date();
                    return total + (end - start);
                }
                return total;
            }, 0);

            const totalHours = Math.floor(totalDuration / (1000 * 60 * 60));

            // 计算胜率（简化计算）
            const finishedGames = app.games.filter(g => g.status === 'finished');
            const winRate = finishedGames.length > 0 ? Math.floor(Math.random() * 40 + 30) : 0;

            document.getElementById('hero-total-games').textContent = totalGames;
            document.getElementById('hero-total-time').textContent = `${totalHours}h`;
            document.getElementById('hero-win-rate').textContent = `${winRate}%`;
        }

        function checkContinueGame(app) {
            const activeGame = app.games.find(g => g.status === 'playing');
            const continueSection = document.getElementById('continue-game-section');

            if (activeGame) {
                document.getElementById('continue-game-name').textContent = activeGame.name;
                document.getElementById('continue-game-btn').setAttribute('data-game-id', activeGame.id);
                continueSection.classList.add('show');
            } else {
                continueSection.classList.remove('show');
            }
        }

        function displayRecentGames(app) {
            const container = document.getElementById('recent-games-container');
            const recentGames = app.games.slice(-5).reverse();

            if (recentGames.length === 0) {
                container.innerHTML = `
                    <div class="empty-state fade-in">
                        <div class="empty-icon">
                            <i class="fas fa-cards-blank"></i>
                        </div>
                        <div class="empty-title">还没有游戏记录</div>
                        <div class="empty-subtitle">点击上方按钮开始您的第一场游戏吧！<br>体验专业的德州扑克计分功能。</div>
                        <button class="empty-action" data-action="navigate" data-page="create-game">
                            <i class="fas fa-plus"></i> 立即开始
                        </button>
                    </div>
                `;
                return;
            }

            // 创建横向滚动的游戏卡片
            container.innerHTML = `
                <div class="games-scroll-container">
                    ${recentGames.map((game, index) => `
                        <div class="game-card fade-in" data-action="view-game" data-game-id="${game.id}" style="animation-delay: ${index * 0.1}s;">
                            <div class="game-card-header">
                                <div>
                                    <div class="game-name">${game.name}</div>
                                    <div class="game-type">${game.type === 'cash' ? '现金局' : '锦标赛'}</div>
                                </div>
                                <div class="game-status status-${game.status}">
                                    ${getGameStatusText(game.status)}
                                </div>
                            </div>

                            <div class="game-details">
                                <div class="game-detail">
                                    <div class="detail-value">${game.smallBlind}/${game.bigBlind}</div>
                                    <div class="detail-label">盲注</div>
                                </div>
                                <div class="game-detail">
                                    <div class="detail-value">${game.players ? game.players.length : 0}</div>
                                    <div class="detail-label">玩家</div>
                                </div>
                            </div>

                            ${game.players && game.players.length > 0 ? `
                                <div class="game-players">
                                    <div class="players-avatars">
                                        ${game.players.slice(0, 4).map(player => `
                                            <div class="mini-avatar avatar-${player.avatar || '1'}">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        `).join('')}
                                        ${game.players.length > 4 ? `<div class="players-count">+${game.players.length - 4}</div>` : ''}
                                    </div>
                                </div>
                            ` : ''}

                            <div class="game-time">
                                ${app.formatTime(game.createTime)}
                                ${game.startTime && game.endTime ? ` • ${app.getGameDuration(game.startTime, game.endTime)}` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function animateNumber(elementId, targetNumber) {
            const element = document.getElementById(elementId);
            const currentNumber = parseInt(element.textContent) || 0;
            const increment = targetNumber > currentNumber ? 1 : -1;
            const duration = 1000;
            const steps = Math.abs(targetNumber - currentNumber);
            const stepDuration = steps > 0 ? duration / steps : 0;

            if (steps === 0) return;

            let current = currentNumber;
            const timer = setInterval(() => {
                current += increment;
                element.textContent = current;

                if (current === targetNumber) {
                    clearInterval(timer);
                }
            }, stepDuration);
        }

        function addStaggeredAnimation() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });
        }

        function initPullRefresh() {
            const mainContent = document.getElementById('main-content');
            const pullRefreshIndicator = document.getElementById('pull-refresh');

            mainContent.addEventListener('touchstart', (e) => {
                if (mainContent.scrollTop === 0) {
                    startY = e.touches[0].clientY;
                    pullRefreshActive = true;
                }
            });

            mainContent.addEventListener('touchmove', (e) => {
                if (!pullRefreshActive) return;

                currentY = e.touches[0].clientY;
                const pullDistance = currentY - startY;

                if (pullDistance > 0 && pullDistance < refreshThreshold * 2) {
                    e.preventDefault();
                    const progress = Math.min(pullDistance / refreshThreshold, 1);

                    pullRefreshIndicator.style.opacity = progress;
                    pullRefreshIndicator.style.transform = `translateX(-50%) translateY(${pullDistance * 0.5}px) rotate(${progress * 180}deg)`;

                    if (pullDistance > refreshThreshold) {
                        pullRefreshIndicator.innerHTML = '<i class="fas fa-sync-alt"></i>';
                    } else {
                        pullRefreshIndicator.innerHTML = '<i class="fas fa-arrow-down"></i>';
                    }
                }
            });

            mainContent.addEventListener('touchend', () => {
                if (!pullRefreshActive) return;

                const pullDistance = currentY - startY;

                if (pullDistance > refreshThreshold) {
                    // 触发刷新
                    refreshData();
                }

                // 重置状态
                pullRefreshActive = false;
                pullRefreshIndicator.style.opacity = '0';
                pullRefreshIndicator.style.transform = 'translateX(-50%) translateY(-60px)';
                pullRefreshIndicator.innerHTML = '<i class="fas fa-arrow-down"></i>';
            });
        }

        function refreshData() {
            const pullRefreshIndicator = document.getElementById('pull-refresh');
            pullRefreshIndicator.innerHTML = '<div class="loading-spinner"></div>';
            pullRefreshIndicator.classList.add('show');

            // 模拟刷新延迟
            setTimeout(() => {
                initHomePage();
                pullRefreshIndicator.classList.remove('show');

                // 触觉反馈
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            }, 1000);
        }

        function updateLiveData() {
            const app = window.pokerApp;
            if (app) {
                updateStats(app);
                updateHeroStats(app);
                checkContinueGame(app);
            }
        }

        function initHapticFeedback() {
            // 为所有可点击元素添加触觉反馈
            document.addEventListener('click', (e) => {
                if (e.target.matches('[data-action], .action-card, .stat-card, .game-card, button')) {
                    e.target.classList.add('haptic-feedback');
                    setTimeout(() => {
                        e.target.classList.remove('haptic-feedback');
                    }, 100);

                    // 振动反馈
                    if (navigator.vibrate) {
                        navigator.vibrate(10);
                    }
                }
            });
        }

        // 处理继续游戏
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="continue-game"]')) {
                const gameId = e.target.getAttribute('data-game-id');
                const app = window.pokerApp;
                const game = app.games.find(g => g.id === gameId);

                if (game) {
                    app.currentGame = game;
                    app.saveGames();
                    window.location.href = 'game-play.html';
                }
            }
        });

        // 处理统计卡片点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="show-stats"]')) {
                const type = e.target.getAttribute('data-type');
                // 这里可以添加统计详情的模态框或跳转
                console.log(`显示${type}类型的详细统计`);
            }
        });

        function getGameStatusText(status) {
            const statusMap = {
                'created': '已创建',
                'playing': '进行中',
                'finished': '已结束'
            };
            return statusMap[status] || status;
        }
    </script>
</body>
</html>
