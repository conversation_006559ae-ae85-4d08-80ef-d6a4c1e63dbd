/* iOS基础样式 - iPhone 15 Pro适配 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* iPhone 15 Pro 尺寸 */
    --device-width: 393px;
    --device-height: 852px;
    --safe-area-top: 59px;
    --safe-area-bottom: 34px;
    --status-bar-height: 44px;
    --nav-bar-height: 44px;
    --home-indicator-height: 34px;
    
    /* iOS 色彩系统 */
    --ios-blue: #007AFF;
    --ios-green: #34C759;
    --ios-red: #FF3B30;
    --ios-orange: #FF9500;
    --ios-yellow: #FFCC00;
    --ios-purple: #AF52DE;
    --ios-pink: #FF2D92;
    --ios-teal: #5AC8FA;
    
    /* 背景色 */
    --background-primary: #F2F2F7;
    --background-secondary: #FFFFFF;
    --background-tertiary: #F2F2F7;
    
    /* 文字色 */
    --text-primary: #000000;
    --text-secondary: #3C3C43;
    --text-tertiary: #3C3C4399;
    --text-quaternary: #3C3C4366;
    
    /* 分割线 */
    --separator: #3C3C4349;
    --separator-opaque: #C6C6C8;
    
    /* 阴影 */
    --shadow-light: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 25px rgba(0,0,0,0.2);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-primary);
    color: var(--text-primary);
    overflow: hidden;
}

/* iPhone 设备框架 */
.device-frame {
    width: var(--device-width);
    height: var(--device-height);
    margin: 20px auto;
    background: #000;
    border-radius: 40px;
    padding: 8px;
    box-shadow: var(--shadow-heavy);
    position: relative;
}

.device-screen {
    width: 100%;
    height: 100%;
    background: var(--background-primary);
    border-radius: 32px;
    overflow: hidden;
    position: relative;
}

/* 状态栏 */
.status-bar {
    height: var(--status-bar-height);
    background: transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 600;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    color: var(--text-primary);
}

.status-left {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 导航栏 */
.nav-bar {
    height: var(--nav-bar-height);
    background: var(--background-secondary);
    border-bottom: 0.5px solid var(--separator);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    position: sticky;
    top: var(--status-bar-height);
    z-index: 999;
}

.nav-title {
    font-size: 17px;
    font-weight: 600;
    color: var(--text-primary);
}

.nav-button {
    background: none;
    border: none;
    color: var(--ios-blue);
    font-size: 17px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s;
}

.nav-button:hover {
    background-color: var(--ios-blue)15;
}

.nav-button:disabled {
    color: var(--text-quaternary);
    cursor: not-allowed;
}

/* 主内容区域 */
.main-content {
    padding-top: calc(var(--status-bar-height) + var(--nav-bar-height));
    padding-bottom: var(--home-indicator-height);
    height: 100vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* Home Indicator */
.home-indicator {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 5px;
    background: var(--text-primary);
    border-radius: 3px;
    opacity: 0.3;
}

/* 按钮样式 */
.btn-primary {
    background: var(--ios-blue);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background: #0056CC;
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--background-secondary);
    color: var(--ios-blue);
    border: 1px solid var(--separator);
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-secondary:hover {
    background: var(--background-tertiary);
}

.btn-destructive {
    background: var(--ios-red);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

/* 卡片样式 */
.card {
    background: var(--background-secondary);
    border-radius: 12px;
    padding: 16px;
    margin: 8px 16px;
    box-shadow: var(--shadow-light);
    border: 0.5px solid var(--separator);
}

/* 列表样式 */
.list-item {
    background: var(--background-secondary);
    border-bottom: 0.5px solid var(--separator);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: background-color 0.2s;
}

.list-item:hover {
    background: var(--background-tertiary);
}

.list-item:first-child {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.list-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

/* 输入框样式 */
.input-field {
    background: var(--background-secondary);
    border: 1px solid var(--separator);
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 17px;
    color: var(--text-primary);
    width: 100%;
    transition: border-color 0.2s;
}

.input-field:focus {
    outline: none;
    border-color: var(--ios-blue);
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.space-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 头像颜色系统 */
.avatar-1 { background: var(--ios-blue); }
.avatar-2 { background: var(--ios-green); }
.avatar-3 { background: var(--ios-red); }
.avatar-4 { background: var(--ios-orange); }
.avatar-5 { background: var(--ios-purple); }
.avatar-6 { background: var(--ios-pink); }

/* 滚动条优化 */
.games-scroll-container {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.games-scroll-container::-webkit-scrollbar {
    display: none;
}

/* 触摸优化 */
.touch-action-manipulation {
    touch-action: manipulation;
}

/* 性能优化 */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* 响应式调整 */
@media (max-width: 400px) {
    .device-frame {
        width: 100vw;
        height: 100vh;
        margin: 0;
        border-radius: 0;
        padding: 0;
    }

    .device-screen {
        border-radius: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .quick-actions {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

@media (max-height: 700px) {
    .hero-section {
        padding: 24px 20px;
    }

    .hero-stats {
        margin-top: 16px;
        padding-top: 16px;
    }
}
