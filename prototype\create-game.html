<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>新建游戏 - 德州扑克计分助手</title>
    <link rel="stylesheet" href="css/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .form-section {
            background: var(--background-secondary);
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }

        .form-header {
            padding: 16px;
            border-bottom: 0.5px solid var(--separator);
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-group {
            padding: 16px;
            border-bottom: 0.5px solid var(--separator);
        }

        .form-group:last-child {
            border-bottom: none;
        }

        .form-label {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
            display: block;
        }

        .form-input {
            width: 100%;
            padding: 12px 0;
            border: none;
            background: transparent;
            font-size: 17px;
            color: var(--text-primary);
        }

        .form-input:focus {
            outline: none;
        }

        .form-input::placeholder {
            color: var(--text-quaternary);
        }

        .game-type-selector {
            display: flex;
            gap: 12px;
        }

        .type-option {
            flex: 1;
            padding: 16px;
            border: 2px solid var(--separator);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: var(--background-tertiary);
        }

        .type-option.selected {
            border-color: var(--ios-blue);
            background: var(--ios-blue)10;
        }

        .type-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .type-description {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .blind-structure {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .blind-input {
            flex: 1;
            text-align: center;
        }

        .blind-separator {
            font-size: 18px;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .preset-chips {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-top: 12px;
        }

        .chip-preset {
            padding: 8px;
            border: 1px solid var(--separator);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: var(--background-tertiary);
            font-size: 14px;
            color: var(--text-primary);
        }

        .chip-preset.selected {
            border-color: var(--ios-blue);
            background: var(--ios-blue)10;
            color: var(--ios-blue);
        }

        .create-button {
            margin: 16px;
            width: calc(100% - 32px);
        }

        .info-card {
            background: var(--ios-blue)10;
            border: 1px solid var(--ios-blue)30;
            border-radius: 12px;
            padding: 16px;
            margin: 16px;
        }

        .info-icon {
            color: var(--ios-blue);
            margin-right: 8px;
        }

        .info-text {
            font-size: 14px;
            color: var(--text-primary);
            line-height: 1.4;
        }

        .advanced-settings {
            margin-top: 12px;
        }

        .toggle-switch {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .switch {
            position: relative;
            width: 50px;
            height: 30px;
            background: var(--separator);
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .switch.active {
            background: var(--ios-green);
        }

        .switch-handle {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .switch.active .switch-handle {
            transform: translateX(20px);
        }
    </style>
</head>
<body>
    <div class="device-frame">
        <div class="device-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal" style="font-size: 12px;"></i>
                    <i class="fas fa-wifi" style="font-size: 12px;"></i>
                    <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" data-action="navigate" data-page="index">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">新建游戏</div>
                <div></div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <form id="create-game-form" data-action="create-game">
                    <!-- 基本信息 -->
                    <div class="form-section fade-in">
                        <div class="form-header">
                            <i class="fas fa-info-circle" style="margin-right: 8px; color: var(--ios-blue);"></i>
                            基本信息
                        </div>
                        <div class="form-group">
                            <label class="form-label">游戏名称</label>
                            <input type="text" name="gameName" class="form-input" placeholder="输入游戏名称" required>
                        </div>
                    </div>

                    <!-- 游戏类型 -->
                    <div class="form-section fade-in">
                        <div class="form-header">
                            <i class="fas fa-gamepad" style="margin-right: 8px; color: var(--ios-blue);"></i>
                            游戏类型
                        </div>
                        <div class="form-group">
                            <div class="game-type-selector">
                                <div class="type-option selected" data-type="cash">
                                    <div class="type-title">现金局</div>
                                    <div class="type-description">筹码代表真实金额</div>
                                </div>
                                <div class="type-option" data-type="tournament">
                                    <div class="type-title">锦标赛</div>
                                    <div class="type-description">固定筹码，淘汰制</div>
                                </div>
                            </div>
                            <input type="hidden" name="gameType" value="cash">
                        </div>
                    </div>

                    <!-- 盲注设置 -->
                    <div class="form-section fade-in">
                        <div class="form-header">
                            <i class="fas fa-coins" style="margin-right: 8px; color: var(--ios-blue);"></i>
                            盲注设置
                        </div>
                        <div class="form-group">
                            <label class="form-label">小盲/大盲</label>
                            <div class="blind-structure">
                                <input type="number" name="smallBlind" class="form-input blind-input" placeholder="小盲" value="5" required>
                                <span class="blind-separator">/</span>
                                <input type="number" name="bigBlind" class="form-input blind-input" placeholder="大盲" value="10" required>
                            </div>
                        </div>
                    </div>

                    <!-- 初始筹码 -->
                    <div class="form-section fade-in">
                        <div class="form-header">
                            <i class="fas fa-stack-overflow" style="margin-right: 8px; color: var(--ios-blue);"></i>
                            初始筹码
                        </div>
                        <div class="form-group">
                            <label class="form-label">每位玩家的起始筹码</label>
                            <input type="number" name="initialChips" class="form-input" placeholder="输入初始筹码" value="1000" required>
                            <div class="preset-chips">
                                <div class="chip-preset" data-chips="500">500</div>
                                <div class="chip-preset selected" data-chips="1000">1000</div>
                                <div class="chip-preset" data-chips="2000">2000</div>
                                <div class="chip-preset" data-chips="5000">5000</div>
                                <div class="chip-preset" data-chips="10000">10000</div>
                                <div class="chip-preset" data-chips="20000">20000</div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级设置 -->
                    <div class="form-section fade-in">
                        <div class="form-header">
                            <i class="fas fa-cog" style="margin-right: 8px; color: var(--ios-blue);"></i>
                            高级设置
                        </div>
                        <div class="form-group">
                            <div class="toggle-switch">
                                <div>
                                    <div style="font-size: 16px; color: var(--text-primary); margin-bottom: 4px;">自动升盲</div>
                                    <div style="font-size: 13px; color: var(--text-secondary);">锦标赛模式下自动提升盲注</div>
                                </div>
                                <div class="switch" id="auto-blind-switch">
                                    <div class="switch-handle"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提示信息 -->
                    <div class="info-card fade-in">
                        <i class="fas fa-lightbulb info-icon"></i>
                        <span class="info-text">
                            建议初始筹码为大盲注的100-200倍，这样可以保证游戏有足够的深度和策略性。
                        </span>
                    </div>

                    <!-- 创建按钮 -->
                    <button type="submit" class="btn-primary create-button fade-in">
                        <i class="fas fa-plus"></i> 创建游戏
                    </button>
                </form>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initCreateGamePage();
        });

        function initCreateGamePage() {
            // 游戏类型选择
            const typeOptions = document.querySelectorAll('.type-option');
            const gameTypeInput = document.querySelector('input[name="gameType"]');
            
            typeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    typeOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    gameTypeInput.value = this.dataset.type;
                    
                    // 根据游戏类型调整界面
                    updateUIForGameType(this.dataset.type);
                });
            });

            // 筹码预设选择
            const chipPresets = document.querySelectorAll('.chip-preset');
            const initialChipsInput = document.querySelector('input[name="initialChips"]');
            
            chipPresets.forEach(preset => {
                preset.addEventListener('click', function() {
                    chipPresets.forEach(p => p.classList.remove('selected'));
                    this.classList.add('selected');
                    initialChipsInput.value = this.dataset.chips;
                });
            });

            // 自动升盲开关
            const autoBlindSwitch = document.getElementById('auto-blind-switch');
            autoBlindSwitch.addEventListener('click', function() {
                this.classList.toggle('active');
            });

            // 盲注联动
            const smallBlindInput = document.querySelector('input[name="smallBlind"]');
            const bigBlindInput = document.querySelector('input[name="bigBlind"]');
            
            smallBlindInput.addEventListener('input', function() {
                const smallBlind = parseInt(this.value) || 0;
                bigBlindInput.value = smallBlind * 2;
            });

            // 表单提交
            const form = document.getElementById('create-game-form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                
                // 验证数据
                if (validateGameData(data)) {
                    // 创建游戏
                    window.pokerApp.createGame(data);
                }
            });
        }

        function updateUIForGameType(type) {
            const autoBlindSection = document.getElementById('auto-blind-switch').closest('.form-group');
            
            if (type === 'tournament') {
                autoBlindSection.style.display = 'block';
            } else {
                autoBlindSection.style.display = 'none';
            }
        }

        function validateGameData(data) {
            // 验证游戏名称
            if (!data.gameName.trim()) {
                alert('请输入游戏名称');
                return false;
            }

            // 验证盲注
            const smallBlind = parseInt(data.smallBlind);
            const bigBlind = parseInt(data.bigBlind);
            
            if (smallBlind <= 0 || bigBlind <= 0) {
                alert('盲注必须大于0');
                return false;
            }

            if (bigBlind <= smallBlind) {
                alert('大盲注必须大于小盲注');
                return false;
            }

            // 验证初始筹码
            const initialChips = parseInt(data.initialChips);
            if (initialChips <= 0) {
                alert('初始筹码必须大于0');
                return false;
            }

            if (initialChips < bigBlind * 10) {
                const confirm = window.confirm('初始筹码较少，可能影响游戏体验。是否继续？');
                if (!confirm) return false;
            }

            return true;
        }
    </script>
</body>
</html>
