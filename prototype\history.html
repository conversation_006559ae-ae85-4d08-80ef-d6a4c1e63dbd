<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>历史记录 - 德州扑克计分助手</title>
    <link rel="stylesheet" href="css/ios-base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .filter-section {
            background: var(--background-secondary);
            margin: 16px;
            border-radius: 12px;
            padding: 16px;
            box-shadow: var(--shadow-light);
        }

        .filter-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .filter-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            background: var(--background-tertiary);
            border: 1px solid var(--separator);
            border-radius: 20px;
            font-size: 14px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-btn.active {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin: 16px;
        }

        .stat-card {
            background: var(--background-secondary);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--separator);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stat-total { color: var(--ios-blue); }
        .stat-active { color: var(--ios-green); }
        .stat-finished { color: var(--text-secondary); }
        .stat-duration { color: var(--ios-orange); }

        .games-list {
            margin: 16px;
        }

        .section-header {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            padding: 0 4px;
        }

        .game-item {
            background: var(--background-secondary);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--separator);
            cursor: pointer;
            transition: all 0.2s;
        }

        .game-item:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }

        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .game-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .game-type {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .game-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
        }

        .status-playing {
            background: var(--ios-green)20;
            color: var(--ios-green);
        }

        .status-finished {
            background: var(--text-quaternary)20;
            color: var(--text-secondary);
        }

        .status-created {
            background: var(--ios-blue)20;
            color: var(--ios-blue);
        }

        .game-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 12px;
        }

        .detail-item {
            text-align: center;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .detail-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .game-time {
            font-size: 13px;
            color: var(--text-tertiary);
            text-align: center;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-subtitle {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 24px;
        }

        .empty-action {
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .empty-action:hover {
            background: #0056CC;
            transform: translateY(-1px);
        }

        .search-bar {
            margin: 16px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid var(--separator);
            border-radius: 10px;
            font-size: 16px;
            background: var(--background-secondary);
            color: var(--text-primary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--ios-blue);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-tertiary);
        }

        .game-players {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
        }

        .player-count {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .player-avatars {
            display: flex;
            gap: 4px;
        }

        .mini-avatar {
            width: 20px;
            height: 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            color: white;
        }

        .load-more {
            text-align: center;
            margin: 20px;
        }

        .load-more-btn {
            background: var(--background-secondary);
            color: var(--ios-blue);
            border: 1px solid var(--separator);
            border-radius: 10px;
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .load-more-btn:hover {
            background: var(--background-tertiary);
        }
    </style>
</head>
<body>
    <div class="device-frame">
        <div class="device-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal" style="font-size: 12px;"></i>
                    <i class="fas fa-wifi" style="font-size: 12px;"></i>
                    <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" data-action="navigate" data-page="index">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">历史记录</div>
                <button class="nav-button" data-action="search">
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 搜索栏 -->
                <div class="search-bar" id="search-bar" style="display: none;">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索游戏名称..." id="search-input">
                </div>

                <!-- 筛选选项 -->
                <div class="filter-section fade-in">
                    <div class="filter-title">
                        <i class="fas fa-filter" style="margin-right: 8px; color: var(--ios-blue);"></i>
                        筛选条件
                    </div>
                    <div class="filter-options">
                        <button class="filter-btn active" data-filter="all">全部</button>
                        <button class="filter-btn" data-filter="playing">进行中</button>
                        <button class="filter-btn" data-filter="finished">已完成</button>
                        <button class="filter-btn" data-filter="cash">现金局</button>
                        <button class="filter-btn" data-filter="tournament">锦标赛</button>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-overview fade-in">
                    <div class="stat-card">
                        <div class="stat-number stat-total" id="total-games-stat">0</div>
                        <div class="stat-label">总游戏数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number stat-active" id="active-games-stat">0</div>
                        <div class="stat-label">进行中</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number stat-finished" id="finished-games-stat">0</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number stat-duration" id="total-duration-stat">0h</div>
                        <div class="stat-label">总时长</div>
                    </div>
                </div>

                <!-- 游戏列表 -->
                <div class="games-list">
                    <div class="section-header">游戏记录</div>
                    <div id="games-container">
                        <!-- 动态加载游戏列表 -->
                    </div>
                </div>

                <!-- 加载更多 -->
                <div class="load-more" id="load-more" style="display: none;">
                    <button class="load-more-btn" onclick="loadMoreGames()">
                        加载更多
                    </button>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        let currentFilter = 'all';
        let searchQuery = '';
        let displayedGames = 0;
        const gamesPerPage = 10;

        document.addEventListener('DOMContentLoaded', function() {
            initHistoryPage();
        });

        function initHistoryPage() {
            const app = window.pokerApp;
            if (!app) {
                setTimeout(initHistoryPage, 100);
                return;
            }

            // 初始化筛选器
            initFilters();
            
            // 初始化搜索
            initSearch();
            
            // 更新统计信息
            updateStats(app);
            
            // 显示游戏列表
            displayGames(app);
        }

        function initFilters() {
            const filterBtns = document.querySelectorAll('.filter-btn');
            
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.filter;
                    displayedGames = 0;
                    displayGames(window.pokerApp);
                });
            });
        }

        function initSearch() {
            const searchBtn = document.querySelector('[data-action="search"]');
            const searchBar = document.getElementById('search-bar');
            const searchInput = document.getElementById('search-input');
            
            searchBtn.addEventListener('click', function() {
                searchBar.style.display = searchBar.style.display === 'none' ? 'block' : 'none';
                if (searchBar.style.display === 'block') {
                    searchInput.focus();
                }
            });
            
            searchInput.addEventListener('input', function() {
                searchQuery = this.value.toLowerCase();
                displayedGames = 0;
                displayGames(window.pokerApp);
            });
        }

        function updateStats(app) {
            const totalGames = app.games.length;
            const activeGames = app.games.filter(g => g.status === 'playing').length;
            const finishedGames = app.games.filter(g => g.status === 'finished').length;
            
            // 计算总时长
            const totalDuration = app.games.reduce((total, game) => {
                if (game.startTime) {
                    const start = new Date(game.startTime);
                    const end = game.endTime ? new Date(game.endTime) : new Date();
                    return total + (end - start);
                }
                return total;
            }, 0);
            
            const totalHours = Math.floor(totalDuration / (1000 * 60 * 60));

            document.getElementById('total-games-stat').textContent = totalGames;
            document.getElementById('active-games-stat').textContent = activeGames;
            document.getElementById('finished-games-stat').textContent = finishedGames;
            document.getElementById('total-duration-stat').textContent = `${totalHours}h`;
        }

        function displayGames(app) {
            const container = document.getElementById('games-container');
            const loadMoreBtn = document.getElementById('load-more');
            
            // 筛选游戏
            let filteredGames = app.games.filter(game => {
                // 状态筛选
                if (currentFilter !== 'all' && game.status !== currentFilter && 
                    !(currentFilter === 'cash' && game.type === 'cash') &&
                    !(currentFilter === 'tournament' && game.type === 'tournament')) {
                    return false;
                }
                
                // 搜索筛选
                if (searchQuery && !game.name.toLowerCase().includes(searchQuery)) {
                    return false;
                }
                
                return true;
            });

            // 按时间倒序排列
            filteredGames.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

            if (filteredGames.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <div class="empty-title">暂无游戏记录</div>
                        <div class="empty-subtitle">
                            ${currentFilter === 'all' ? '还没有任何游戏记录' : '没有符合条件的游戏记录'}
                        </div>
                        <button class="empty-action" data-action="navigate" data-page="create-game">
                            <i class="fas fa-plus"></i> 开始新游戏
                        </button>
                    </div>
                `;
                loadMoreBtn.style.display = 'none';
                return;
            }

            // 分页显示
            const gamesToShow = filteredGames.slice(0, displayedGames + gamesPerPage);
            displayedGames = gamesToShow.length;

            container.innerHTML = gamesToShow.map(game => `
                <div class="game-item fade-in" data-action="view-game" data-game-id="${game.id}">
                    <div class="game-header">
                        <div>
                            <div class="game-name">${game.name}</div>
                            <div class="game-type">${game.type === 'cash' ? '现金局' : '锦标赛'}</div>
                        </div>
                        <div class="game-status ${getStatusClass(game.status)}">
                            ${getStatusText(game.status)}
                        </div>
                    </div>
                    
                    <div class="game-details">
                        <div class="detail-item">
                            <div class="detail-value">${game.smallBlind}/${game.bigBlind}</div>
                            <div class="detail-label">盲注</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-value">${game.players ? game.players.length : 0}</div>
                            <div class="detail-label">玩家数</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-value">${game.initialChips.toLocaleString()}</div>
                            <div class="detail-label">初始筹码</div>
                        </div>
                    </div>
                    
                    ${game.players && game.players.length > 0 ? `
                        <div class="game-players">
                            <span class="player-count">${game.players.length}名玩家:</span>
                            <div class="player-avatars">
                                ${game.players.slice(0, 5).map(player => `
                                    <div class="mini-avatar avatar-${player.avatar || '1'}">
                                        <i class="fas fa-user"></i>
                                    </div>
                                `).join('')}
                                ${game.players.length > 5 ? '<span style="font-size: 12px; color: var(--text-secondary);">...</span>' : ''}
                            </div>
                        </div>
                    ` : ''}
                    
                    <div class="game-time">
                        ${app.formatTime(game.createTime)}
                        ${game.startTime && game.endTime ? ` • 时长: ${app.getGameDuration(game.startTime, game.endTime)}` : ''}
                    </div>
                </div>
            `).join('');

            // 显示/隐藏加载更多按钮
            loadMoreBtn.style.display = displayedGames < filteredGames.length ? 'block' : 'none';
        }

        function loadMoreGames() {
            displayGames(window.pokerApp);
        }

        function getStatusClass(status) {
            const classMap = {
                'created': 'status-created',
                'playing': 'status-playing',
                'finished': 'status-finished'
            };
            return classMap[status] || '';
        }

        function getStatusText(status) {
            const textMap = {
                'created': '已创建',
                'playing': '进行中',
                'finished': '已完成'
            };
            return textMap[status] || status;
        }
    </script>
</body>
</html>
